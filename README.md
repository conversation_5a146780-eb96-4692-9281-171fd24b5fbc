# Network Packet Capture Agent

A high-performance, production-ready network packet capture agent that captures TCP packets, reassembles them into HTTP requests/responses, and sends the data to Apache RocketMQ for further processing.

## Features

- **Real-time Packet Capture**: Captures TCP packets on specified network interfaces and ports
- **HTTP Reassembly**: Intelligently reassembles TCP packets into complete HTTP requests and responses
- **Message Queue Integration**: Sends reassembled HTTP data to Apache RocketMQ
- **Web Dashboard**: User-friendly web interface for monitoring and control
- **RESTful API**: Comprehensive REST API for programmatic control
- **Performance Optimized**: Multi-threaded processing with configurable performance tuning
- **Production Ready**: Comprehensive logging, monitoring, and error handling
- **Docker Support**: Easy deployment with Docker and Docker Compose
- **Configuration Management**: Flexible configuration via YAML files and environment variables

## Quick Start

### Prerequisites

- Java 8 or higher
- Maven 3.6+
- Apache RocketMQ (or use the provided Docker Compose setup)
- Network interface with appropriate permissions for packet capture

### Option 1: Docker Compose (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd net-pcap-agent
```

2. Set environment variables (optional):
```bash
export HOST_IP=*************
export DEFAULT_PORT=8080
```

3. Start the services:
```bash
docker-compose up -d
```

4. Access the dashboard:
- Web Dashboard: http://localhost:25900/agent/net-pcap/dashboard
- API Documentation: http://localhost:25900/agent/net-pcap/api/v1
- RocketMQ Console: http://localhost:8080

### Option 2: Manual Installation

1. Build the application:
```bash
mvn clean package
```

2. Configure RocketMQ connection in `application.yml`:
```yaml
rocketmq:
  name-server: localhost:9876
```

3. Run the application:
```bash
java -jar target/net-pcap-agent-0.0.1-SNAPSHOT.jar
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST_IP` | 127.0.0.1 | Host IP for distinguishing request/response packets |
| `ROCKETMQ_NAME_SERVER` | localhost:9876 | RocketMQ NameServer address |
| `DEFAULT_PORT` | 80 | Default port to capture |
| `WORKER_THREADS` | 4 | Number of worker threads for packet processing |
| `BUFFER_MAX_SIZE` | 10000 | Maximum number of packets to buffer |
| `REASSEMBLY_CONFIRM_DURATION` | 5 | Confirmation duration in seconds |

### Application Configuration

The application can be configured via `application.yml`:

```yaml
packet-capture:
  host-ip: *************
  default-port: 8080
  buffer:
    max-size: 10000
    cleanup-interval: 60
    max-age: 300
  reassembly:
    default-confirm-duration: 5
    max-wait-time: 30
    batch-size: 100
  performance:
    worker-threads: 4
    pcap-buffer-size: 65536
    pcap-timeout: 1000
```

## API Reference

### Start Packet Capture
```http
POST /api/v1/capture/start
Content-Type: application/x-www-form-urlencoded

deviceName=eth0&port=8080
```

### Stop Packet Capture
```http
POST /api/v1/capture/stop
```

### Get Capture Status
```http
GET /api/v1/capture/status
```

### Get Processing Statistics
```http
GET /api/v1/stats
```

### Get Network Interfaces
```http
GET /api/v1/interfaces
```

## Web Dashboard

The web dashboard provides:

- **Real-time monitoring** of packet capture status
- **Performance metrics** and statistics
- **Control panel** for starting/stopping capture
- **Configuration management**
- **Session monitoring** for request/response pairs

Access the dashboard at: `http://localhost:25900/agent/net-pcap/dashboard`

## Architecture

### Components

1. **PacketCaptureService**: Manages network interface capture using pcap4j
2. **PacketProcessingService**: Processes and reassembles TCP packets
3. **RocketMQ Integration**: Sends reassembled HTTP data to message queue
4. **Web Dashboard**: Provides user interface for monitoring and control
5. **REST API**: Enables programmatic control and monitoring

### Data Flow

1. **Capture**: TCP packets are captured from specified network interface
2. **Classification**: Packets are classified as requests or responses based on destination IP
3. **Buffering**: Packets are buffered in thread-safe collections
4. **Reassembly**: TCP packets are reassembled into HTTP requests/responses
5. **Publishing**: Complete HTTP pairs are sent to RocketMQ

## Monitoring

### Health Checks

- Application health: `/actuator/health`
- Metrics: `/actuator/metrics`
- Prometheus metrics: `/actuator/prometheus`

### Logging

Logs are written to:
- Console (configurable level)
- File: `logs/net-pcap-agent.log` (with rotation)

### Performance Metrics

- Processed packets count
- Reassembled sessions count
- Sent messages count
- Error count
- Current buffer size
- Dropped packets count

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the application has sufficient privileges for packet capture
2. **No Network Interfaces**: Check if pcap libraries are properly installed
3. **RocketMQ Connection**: Verify RocketMQ is running and accessible
4. **High Memory Usage**: Adjust buffer sizes and cleanup intervals

### Performance Tuning

- Increase `worker-threads` for high-traffic environments
- Adjust `buffer.max-size` based on available memory
- Tune `reassembly.batch-size` for optimal throughput
- Monitor and adjust `pcap-buffer-size` for packet loss prevention

## Development

### Building from Source

```bash
git clone <repository-url>
cd net-pcap-agent
mvn clean package
```

### Running Tests

```bash
mvn test
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details
