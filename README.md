# Network Packet Capture Agent

A high-performance, production-ready network packet capture agent that captures TCP packets, reassembles them into HTTP requests/responses, and sends the data to Apache RocketMQ for further processing.

## Features

- **Real-time Packet Capture**: Captures TCP packets on specified network interfaces and ports
- **HTTP Reassembly**: Intelligently reassembles TCP packets into complete HTTP requests and responses
- **Message Queue Integration**: Sends reassembled HTTP data to Apache RocketMQ
- **Web Dashboard**: User-friendly web interface for monitoring and control
- **RESTful API**: Comprehensive REST API for programmatic control
- **Performance Optimized**: Multi-threaded processing with configurable performance tuning
- **Production Ready**: Comprehensive logging, monitoring, and error handling
- **Easy Deployment**: Simple JAR-based deployment with startup scripts
- **Configuration Management**: Flexible configuration via YAML files and environment variables

## Quick Start

### Prerequisites

- Java 8 or higher
- Maven 3.6+
- Apache RocketMQ server (running separately)
- Network interface with appropriate permissions for packet capture

### Option 1: Using Startup Script (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd net-pcap-agent
```

2. Build and start with default configuration:
```bash
./start.sh --build
```

3. Or start with custom configuration:
```bash
./start.sh --host-ip ************* --port 8080 --rocketmq-server *************:9876
```

4. Access the dashboard:
- Web Dashboard: http://localhost:25900/agent/net-pcap/dashboard
- API Documentation: http://localhost:25900/agent/net-pcap/api/v1

### Option 2: Manual JAR Execution

1. Build the application:
```bash
mvn clean package
```

2. Configure RocketMQ connection in `application.yml` or via environment variables:
```bash
export ROCKETMQ_NAME_SERVER=localhost:9876
export HOST_IP=*************
```

3. Run the application:
```bash
java -jar target/net-pcap-agent-0.0.1-SNAPSHOT.jar
```

### Option 3: Background Service

Start the application as a background service:
```bash
./start.sh --background --host-ip ************* --port 8080
```

Stop the background service:
```bash
# Find the PID from net-pcap-agent.pid file
kill $(cat net-pcap-agent.pid) && rm net-pcap-agent.pid
```

## Configuration

### Startup Script Options

The `start.sh` script supports the following options:

| Option | Description | Default |
|--------|-------------|---------|
| `--host-ip IP` | Host IP for distinguishing request/response packets | 127.0.0.1 |
| `--port PORT` | Default capture port | 80 |
| `--rocketmq-server SERVER` | RocketMQ server address | localhost:9876 |
| `--worker-threads THREADS` | Number of worker threads | 4 |
| `--profile PROFILE` | Spring profile (dev/prod) | prod |
| `--build` | Build before starting | false |
| `--dev` | Start in development mode | false |
| `--background` | Start as background service | false |
| `--jvm-opts OPTS` | Additional JVM options | - |

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST_IP` | 127.0.0.1 | Host IP for distinguishing request/response packets |
| `ROCKETMQ_NAME_SERVER` | localhost:9876 | RocketMQ NameServer address |
| `DEFAULT_PORT` | 80 | Default port to capture |
| `WORKER_THREADS` | 4 | Number of worker threads for packet processing |
| `BUFFER_MAX_SIZE` | 10000 | Maximum number of packets to buffer |
| `REASSEMBLY_CONFIRM_DURATION` | 5 | Confirmation duration in seconds |

### Application Configuration

The application can be configured via `application.yml`:

```yaml
packet-capture:
  host-ip: *************
  default-port: 8080
  buffer:
    max-size: 10000
    cleanup-interval: 60
    max-age: 300
  reassembly:
    default-confirm-duration: 5
    max-wait-time: 30
    batch-size: 100
  performance:
    worker-threads: 4
    pcap-buffer-size: 65536
    pcap-timeout: 1000
```

### Profile-Specific Configuration

Use different profiles for different environments:

**Development Profile** (`--dev` or `--profile dev`):
- Debug logging enabled
- Smaller buffer sizes
- Faster processing for development

**Production Profile** (`--profile prod`):
- Optimized for performance
- Larger buffers
- Conservative error handling

## API Reference

### Start Packet Capture
```http
POST /api/v1/capture/start
Content-Type: application/x-www-form-urlencoded

deviceName=eth0&port=8080
```

### Stop Packet Capture
```http
POST /api/v1/capture/stop
```

### Get Capture Status
```http
GET /api/v1/capture/status
```

### Get Processing Statistics
```http
GET /api/v1/stats
```

### Get Network Interfaces
```http
GET /api/v1/interfaces
```

## Web Dashboard

The web dashboard provides:

- **Real-time monitoring** of packet capture status
- **Performance metrics** and statistics
- **Control panel** for starting/stopping capture
- **Configuration management**
- **Session monitoring** for request/response pairs

Access the dashboard at: `http://localhost:25900/agent/net-pcap/dashboard`

## Deployment

### Production Deployment

1. **Build the application**:
```bash
mvn clean package -Dmaven.test.skip=true
```

2. **Create a deployment directory**:
```bash
mkdir -p /opt/net-pcap-agent
cp target/net-pcap-agent-*.jar /opt/net-pcap-agent/
cp start.sh /opt/net-pcap-agent/
chmod +x /opt/net-pcap-agent/start.sh
```

3. **Create configuration file**:
```bash
# Create application-prod.yml with production settings
cp src/main/resources/application-prod.yml /opt/net-pcap-agent/
```

4. **Start the service**:
```bash
cd /opt/net-pcap-agent
./start.sh --profile prod --background --host-ip <your-host-ip> --rocketmq-server <rocketmq-server>
```

### Service Management

Create a systemd service for automatic startup:

```bash
# Create /etc/systemd/system/net-pcap-agent.service
[Unit]
Description=Network Packet Capture Agent
After=network.target

[Service]
Type=forking
User=pcap-user
WorkingDirectory=/opt/net-pcap-agent
ExecStart=/opt/net-pcap-agent/start.sh --background --profile prod
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/net-pcap-agent/net-pcap-agent.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl enable net-pcap-agent
sudo systemctl start net-pcap-agent
sudo systemctl status net-pcap-agent
```

## Architecture

### Components

1. **PacketCaptureService**: Manages network interface capture using pcap4j
2. **PacketProcessingService**: Processes and reassembles TCP packets
3. **RocketMQ Integration**: Sends reassembled HTTP data to message queue
4. **Web Dashboard**: Provides user interface for monitoring and control
5. **REST API**: Enables programmatic control and monitoring

### Data Flow

1. **Capture**: TCP packets are captured from specified network interface
2. **Classification**: Packets are classified as requests or responses based on destination IP
3. **Buffering**: Packets are buffered in thread-safe collections
4. **Reassembly**: TCP packets are reassembled into HTTP requests/responses
5. **Publishing**: Complete HTTP pairs are sent to RocketMQ

## Monitoring

### Health Checks

- Application health: `/actuator/health`
- Metrics: `/actuator/metrics`
- Prometheus metrics: `/actuator/prometheus`

### Logging

Logs are written to:
- Console (configurable level)
- File: `logs/net-pcap-agent.log` (with rotation)

### Performance Metrics

- Processed packets count
- Reassembled sessions count
- Sent messages count
- Error count
- Current buffer size
- Dropped packets count

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the application has sufficient privileges for packet capture
   ```bash
   # Run with sudo if needed
   sudo ./start.sh --host-ip *************
   ```

2. **No Network Interfaces**: Check if pcap libraries are properly installed
   ```bash
   # On Ubuntu/Debian
   sudo apt-get install libpcap-dev

   # On CentOS/RHEL
   sudo yum install libpcap-devel
   ```

3. **RocketMQ Connection**: Verify RocketMQ is running and accessible
   ```bash
   # Test connection
   telnet <rocketmq-host> 9876
   ```

4. **High Memory Usage**: Adjust buffer sizes and cleanup intervals
   ```bash
   ./start.sh --jvm-opts "-Xmx1g -XX:+UseG1GC" --host-ip *************
   ```

5. **Application Already Running**: Check for existing processes
   ```bash
   # Check if PID file exists
   cat net-pcap-agent.pid

   # Kill existing process
   kill $(cat net-pcap-agent.pid) && rm net-pcap-agent.pid
   ```

### Performance Tuning

- **High Traffic**: Increase worker threads and buffer sizes
  ```bash
  ./start.sh --worker-threads 8 --jvm-opts "-Xmx2g"
  ```

- **Memory Optimization**: Tune JVM garbage collection
  ```bash
  ./start.sh --jvm-opts "-Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
  ```

- **Network Optimization**: Adjust pcap buffer size in configuration
  ```yaml
  packet-capture:
    performance:
      pcap-buffer-size: 131072  # Larger buffer for high traffic
  ```

### Log Analysis

View application logs:
```bash
# Real-time logs
tail -f logs/net-pcap-agent.log

# Background service logs
tail -f logs/application.log

# Search for errors
grep ERROR logs/net-pcap-agent.log
```

## Development

### Building from Source

```bash
git clone <repository-url>
cd net-pcap-agent
mvn clean package
```

### Development Mode

Start in development mode with debug logging:
```bash
./start.sh --dev --build
```

### Running Tests

```bash
mvn test
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the logs for error details
- Use the web dashboard for real-time monitoring
