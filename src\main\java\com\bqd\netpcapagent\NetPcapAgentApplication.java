package com.bqd.netpcapagent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Main application class for Network Packet Capture Agent
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@SpringBootApplication
@EnableConfigurationProperties
public class NetPcapAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(NetPcapAgentApplication.class, args);
    }
}
