version: '3.8'

services:
  net-pcap-agent:
    build: .
    container_name: net-pcap-agent
    ports:
      - "25900:25900"
    environment:
      - HOST_IP=${HOST_IP:-127.0.0.1}
      - ROCKETMQ_NAME_SERVER=${ROCKETMQ_NAME_SERVER:-rocketmq:9876}
      - DEFAULT_PORT=${DEFAULT_PORT:-80}
      - WORKER_THREADS=${WORKER_THREADS:-4}
      - BUFFER_MAX_SIZE=${BUFFER_MAX_SIZE:-10000}
      - REASSEMBLY_CONFIRM_DURATION=${REASSEMBLY_CONFIRM_DURATION:-5}
    volumes:
      - ./logs:/app/logs
    networks:
      - pcap-network
    depends_on:
      - rocketmq
    restart: unless-stopped
    # Required for packet capture
    cap_add:
      - NET_ADMIN
      - NET_RAW
    # Use host network for packet capture (optional)
    # network_mode: host

  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    environment:
      - JAVA_OPT_EXT=-Xms512m -Xmx512m
    command: sh mqnamesrv
    networks:
      - pcap-network
    restart: unless-stopped

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:4.9.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      - NAMESRV_ADDR=rocketmq-nameserver:9876
      - JAVA_OPT_EXT=-Xms512m -Xmx512m
    command: sh mqbroker -n rocketmq-nameserver:9876 -c /opt/rocketmq-4.9.4/conf/broker.conf
    depends_on:
      - rocketmq-nameserver
    networks:
      - pcap-network
    restart: unless-stopped
    volumes:
      - rocketmq-broker-data:/home/<USER>/store

  # RocketMQ Console (Optional - for monitoring)
  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: rocketmq-console
    ports:
      - "8080:8080"
    environment:
      - JAVA_OPTS=-Xms256m -Xmx256m -Drocketmq.namesrv.addr=rocketmq-nameserver:9876
    depends_on:
      - rocketmq-nameserver
    networks:
      - pcap-network
    restart: unless-stopped

networks:
  pcap-network:
    driver: bridge

volumes:
  rocketmq-broker-data:
