package com.bqd.netpcapagent.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pcap4j.packet.TcpPacket;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a TCP session containing related packets
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcpSession {
    
    /**
     * List of TCP packets in this session
     */
    private List<TcpPacket> tcpPacketList = new ArrayList<>();
    
    /**
     * Session creation time
     */
    private LocalDateTime createTime = LocalDateTime.now();
    
    /**
     * Last update time
     */
    private LocalDateTime lastUpdateTime = LocalDateTime.now();
    
    /**
     * Session identifier (acknowledgment number)
     */
    private Long sessionId;
    
    /**
     * Whether this session is complete
     */
    private boolean complete = false;
    
    /**
     * Session type (REQUEST or RESPONSE)
     */
    private SessionType type;
    
    /**
     * Source IP address
     */
    private String sourceIp;
    
    /**
     * Destination IP address
     */
    private String destinationIp;
    
    /**
     * Source port
     */
    private int sourcePort;
    
    /**
     * Destination port
     */
    private int destinationPort;
    
    public enum SessionType {
        REQUEST, RESPONSE
    }
    
    /**
     * Add a packet to this session
     * 
     * @param packet the packet to add
     */
    public void addPacket(TcpPacket packet) {
        this.tcpPacketList.add(packet);
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * Get the total payload size of all packets in this session
     * 
     * @return total payload size in bytes
     */
    public int getTotalPayloadSize() {
        return tcpPacketList.stream()
                .filter(packet -> packet.getPayload() != null)
                .mapToInt(packet -> packet.getPayload().getRawData().length)
                .sum();
    }
    
    /**
     * Check if this session has expired based on the given timeout
     * 
     * @param timeoutSeconds timeout in seconds
     * @return true if expired
     */
    public boolean isExpired(int timeoutSeconds) {
        return lastUpdateTime.isBefore(LocalDateTime.now().minusSeconds(timeoutSeconds));
    }
}
