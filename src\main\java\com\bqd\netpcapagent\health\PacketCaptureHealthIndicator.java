package com.bqd.netpcapagent.health;

import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * Health indicator for packet capture functionality
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Component
public class PacketCaptureHealthIndicator implements HealthIndicator {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @Override
    public Health health() {
        try {
            Health.Builder builder = new Health.Builder();
            
            // Check capture service status
            boolean captureRunning = packetCaptureService.isCaptureRunning();
            boolean reassemblyRunning = packetProcessingService.isReassemblyRunning();
            
            // Get current statistics
            PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
            
            // Determine overall health
            if (captureRunning && reassemblyRunning) {
                builder.up();
            } else if (!captureRunning && !reassemblyRunning) {
                // Both stopped - this might be intentional
                builder.up().withDetail("status", "stopped");
            } else {
                // Inconsistent state
                builder.down().withDetail("status", "inconsistent");
            }
            
            // Add detailed information
            builder.withDetail("captureRunning", captureRunning)
                   .withDetail("reassemblyRunning", reassemblyRunning)
                   .withDetail("processedPackets", stats.getProcessedPackets())
                   .withDetail("reassembledSessions", stats.getReassembledSessions())
                   .withDetail("sentMessages", stats.getSentMessages())
                   .withDetail("errorCount", stats.getErrorCount())
                   .withDetail("currentBufferSize", stats.getCurrentBufferSize())
                   .withDetail("droppedPackets", stats.getDroppedPackets());
            
            // Add current configuration if capture is running
            PacketCaptureService.CaptureConfig config = packetCaptureService.getCurrentConfig();
            if (config != null) {
                builder.withDetail("currentDevice", config.getDeviceName())
                       .withDetail("currentPort", config.getPort())
                       .withDetail("captureStartTime", config.getStartTime())
                       .withDetail("capturedPackets", config.getCapturedPackets());
            }
            
            // Check for potential issues
            if (stats.getErrorCount() > 0) {
                builder.withDetail("warning", "Errors detected during processing");
            }
            
            if (stats.getDroppedPackets() > 0) {
                builder.withDetail("warning", "Packets have been dropped due to buffer overflow");
            }
            
            return builder.build();
            
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", "Failed to check packet capture health")
                    .withDetail("exception", e.getMessage())
                    .build();
        }
    }
}
