package com.bqd.netpcapagent.service;

import com.bqd.netpcapagent.model.TcpSession;
import org.pcap4j.packet.Packet;

import java.util.List;

/**
 * Service interface for packet processing operations
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
public interface PacketProcessingService {

    /**
     * Process a captured packet
     *
     * @param packet the captured packet
     */
    void processPacket(Packet packet);

    /**
     * Start the packet reassembly and sending process
     */
    void startReassemblyProcess();

    /**
     * Stop the packet reassembly and sending process
     */
    void stopReassemblyProcess();

    /**
     * Get current request packet sessions
     *
     * @return list of request sessions
     */
    List<TcpSession> getRequestSessions();

    /**
     * Get current response packet sessions
     *
     * @return list of response sessions
     */
    List<TcpSession> getResponseSessions();

    /**
     * Clear all packet buffers
     */
    void clearBuffers();

    /**
     * Get processing statistics
     *
     * @return processing statistics
     */
    ProcessingStats getStats();

    /**
     * Check if reassembly process is running
     *
     * @return true if running
     */
    boolean isReassemblyRunning();

    /**
     * Processing statistics
     */
    interface ProcessingStats {
        long getProcessedPackets();
        long getReassembledSessions();
        long getSentMessages();
        long getErrorCount();
        int getCurrentBufferSize();
        long getDroppedPackets();
    }
}
