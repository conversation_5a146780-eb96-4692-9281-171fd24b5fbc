package com.bqd.netpcapagent.service.impl;

import com.bqd.netpcapagent.config.PacketCaptureProperties;
import com.bqd.netpcapagent.exception.PacketCaptureException;
import com.bqd.netpcapagent.model.HttpPacketDto;
import com.bqd.netpcapagent.model.TcpSession;
import com.bqd.netpcapagent.service.PacketProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.pcap4j.packet.IpV4Packet;
import org.pcap4j.packet.Packet;
import org.pcap4j.packet.TcpPacket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implementation of packet processing service with improved architecture
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Slf4j
@Service
public class PacketProcessingServiceImpl implements PacketProcessingService {

    @Autowired
    private PacketCaptureProperties properties;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    // Thread-safe collections for storing sessions
    private final ConcurrentHashMap<Long, TcpSession> requestSessions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, TcpSession> responseSessions = new ConcurrentHashMap<>();

    // Thread pool for processing
    private ExecutorService processingExecutor;
    private ScheduledExecutorService cleanupExecutor;

    // Control flags
    private final AtomicBoolean reassemblyRunning = new AtomicBoolean(false);

    // Statistics
    private final AtomicLong processedPackets = new AtomicLong(0);
    private final AtomicLong reassembledSessions = new AtomicLong(0);
    private final AtomicLong sentMessages = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong droppedPackets = new AtomicLong(0);

    @PostConstruct
    public void init() {
        // Initialize thread pools
        processingExecutor = Executors.newFixedThreadPool(
                properties.getPerformance().getWorkerThreads(),
                r -> {
                    Thread t = new Thread(r, "packet-processor");
                    t.setDaemon(true);
                    return t;
                }
        );

        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
                r -> {
                    Thread t = new Thread(r, "buffer-cleanup");
                    t.setDaemon(true);
                    return t;
                }
        );

        // Start periodic cleanup
        cleanupExecutor.scheduleAtFixedRate(
                this::cleanupExpiredSessions,
                properties.getBuffer().getCleanupInterval(),
                properties.getBuffer().getCleanupInterval(),
                TimeUnit.SECONDS
        );

        log.info("Packet processing service initialized with {} worker threads", 
                properties.getPerformance().getWorkerThreads());
    }

    @PreDestroy
    public void destroy() {
        stopReassemblyProcess();
        
        if (processingExecutor != null) {
            processingExecutor.shutdown();
            try {
                if (!processingExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    processingExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                processingExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (cleanupExecutor != null) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("Packet processing service destroyed");
    }

    @Override
    public void processPacket(Packet packet) {
        try {
            // Check buffer size limits
            int currentBufferSize = requestSessions.size() + responseSessions.size();
            if (currentBufferSize >= properties.getBuffer().getMaxSize()) {
                droppedPackets.incrementAndGet();
                log.warn("Buffer full, dropping packet. Current size: {}", currentBufferSize);
                return;
            }

            // Submit packet processing to thread pool
            processingExecutor.submit(() -> doProcessPacket(packet));
            
        } catch (RejectedExecutionException e) {
            droppedPackets.incrementAndGet();
            log.warn("Packet processing rejected due to thread pool capacity", e);
        }
    }

    private void doProcessPacket(Packet packet) {
        try {
            // Extract TCP packet
            TcpPacket tcpPacket = packet.get(TcpPacket.class);
            if (tcpPacket == null || tcpPacket.getPayload() == null) {
                return;
            }

            // Extract IPv4 packet
            IpV4Packet ipV4Packet = packet.get(IpV4Packet.class);
            if (ipV4Packet == null) {
                return;
            }

            processedPackets.incrementAndGet();

            // Determine if this is a request or response based on destination IP
            boolean isRequest = properties.getHostIp().equals(
                    ipV4Packet.getHeader().getDstAddr().getHostAddress());

            if (isRequest) {
                addPacketToSession(requestSessions, tcpPacket, ipV4Packet, TcpSession.SessionType.REQUEST);
            } else {
                addPacketToSession(responseSessions, tcpPacket, ipV4Packet, TcpSession.SessionType.RESPONSE);
            }

        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("Error processing packet", e);
        }
    }

    private void addPacketToSession(ConcurrentHashMap<Long, TcpSession> sessionMap, 
                                   TcpPacket tcpPacket, IpV4Packet ipV4Packet, 
                                   TcpSession.SessionType type) {
        
        long ackNo = tcpPacket.getHeader().getAcknowledgmentNumberAsLong();
        
        TcpSession session = sessionMap.computeIfAbsent(ackNo, k -> {
            TcpSession newSession = new TcpSession();
            newSession.setSessionId(ackNo);
            newSession.setType(type);
            newSession.setSourceIp(ipV4Packet.getHeader().getSrcAddr().getHostAddress());
            newSession.setDestinationIp(ipV4Packet.getHeader().getDstAddr().getHostAddress());
            newSession.setSourcePort(tcpPacket.getHeader().getSrcPort().valueAsInt());
            newSession.setDestinationPort(tcpPacket.getHeader().getDstPort().valueAsInt());
            return newSession;
        });
        
        session.addPacket(tcpPacket);
    }

    @Override
    public void startReassemblyProcess() {
        if (reassemblyRunning.compareAndSet(false, true)) {
            processingExecutor.submit(this::runReassemblyLoop);
            log.info("Packet reassembly process started");
        } else {
            log.warn("Reassembly process is already running");
        }
    }

    @Override
    public void stopReassemblyProcess() {
        if (reassemblyRunning.compareAndSet(true, false)) {
            log.info("Packet reassembly process stopped");
        }
    }

    private void runReassemblyLoop() {
        log.info("Starting reassembly loop");
        
        while (reassemblyRunning.get() || !responseSessions.isEmpty()) {
            try {
                processReassemblyBatch();
                Thread.sleep(1000); // Process every second
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                errorCount.incrementAndGet();
                log.error("Error in reassembly loop", e);
            }
        }
        
        log.info("Reassembly loop ended");
    }

    private void processReassemblyBatch() {
        LocalDateTime now = LocalDateTime.now();
        long confirmDurationSeconds = properties.getReassembly().getDefaultConfirmDuration();
        
        List<Long> expiredSessionIds = responseSessions.entrySet().stream()
                .filter(entry -> entry.getValue().getLastUpdateTime()
                        .isBefore(now.minusSeconds(confirmDurationSeconds)))
                .map(Map.Entry::getKey)
                .limit(properties.getReassembly().getBatchSize())
                .collect(Collectors.toList());

        for (Long sessionId : expiredSessionIds) {
            processExpiredSession(sessionId);
        }
    }

    private void processExpiredSession(Long responseSessionId) {
        TcpSession responseSession = responseSessions.remove(responseSessionId);
        if (responseSession == null) {
            return;
        }

        try {
            // Find corresponding request session
            Long requestSessionId = findRequestSessionId(responseSession);
            TcpSession requestSession = requestSessions.remove(requestSessionId);
            
            if (requestSession == null) {
                log.debug("No matching request session found for response session {}", responseSessionId);
                return;
            }

            // Reassemble HTTP packets
            String request = reassembleToHttp(requestSession.getTcpPacketList());
            String response = reassembleToHttp(responseSession.getTcpPacketList());

            if (request.trim().isEmpty() || response.trim().isEmpty()) {
                log.debug("Empty request or response, skipping session {}", responseSessionId);
                return;
            }

            // Create and send HTTP packet DTO
            HttpPacketDto httpPacket = new HttpPacketDto(
                    request,
                    requestSession.getCreateTime().toEpochSecond(ZoneOffset.UTC) * 1000,
                    response,
                    responseSession.getCreateTime().toEpochSecond(ZoneOffset.UTC) * 1000
            );

            rocketMQTemplate.sendOneWay("net-pcap-agent", httpPacket);
            
            reassembledSessions.incrementAndGet();
            sentMessages.incrementAndGet();
            
            log.debug("Successfully processed session pair: request={}, response={}", 
                    requestSessionId, responseSessionId);

        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("Error processing expired session {}", responseSessionId, e);
        }
    }

    private Long findRequestSessionId(TcpSession responseSession) {
        if (responseSession.getTcpPacketList().isEmpty()) {
            return null;
        }
        
        // Use the sequence number from the first response packet to find the request
        return responseSession.getTcpPacketList().get(0).getHeader().getSequenceNumberAsLong();
    }

    private String reassembleToHttp(List<TcpPacket> tcpPacketList) {
        StringBuilder stringBuilder = new StringBuilder();
        
        tcpPacketList.stream()
                .filter(packet -> packet.getPayload() != null)
                .sorted((t1, t2) -> Long.compare(
                        t1.getHeader().getSequenceNumberAsLong(),
                        t2.getHeader().getSequenceNumberAsLong()))
                .forEach(tcpPacket -> {
                    byte[] payload = tcpPacket.getPayload().getRawData();
                    stringBuilder.append(new String(payload));
                });
        
        return stringBuilder.toString();
    }

    private void cleanupExpiredSessions() {
        try {
            int maxAge = properties.getBuffer().getMaxAge();
            LocalDateTime cutoff = LocalDateTime.now().minusSeconds(maxAge);
            
            int removedRequests = cleanupSessionMap(requestSessions, cutoff);
            int removedResponses = cleanupSessionMap(responseSessions, cutoff);
            
            if (removedRequests > 0 || removedResponses > 0) {
                log.info("Cleaned up {} expired request sessions and {} expired response sessions", 
                        removedRequests, removedResponses);
            }
            
        } catch (Exception e) {
            log.error("Error during session cleanup", e);
        }
    }

    private int cleanupSessionMap(ConcurrentHashMap<Long, TcpSession> sessionMap, LocalDateTime cutoff) {
        List<Long> expiredIds = sessionMap.entrySet().stream()
                .filter(entry -> entry.getValue().getCreateTime().isBefore(cutoff))
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());
        
        expiredIds.forEach(sessionMap::remove);
        return expiredIds.size();
    }

    @Override
    public List<TcpSession> getRequestSessions() {
        return new ArrayList<>(requestSessions.values());
    }

    @Override
    public List<TcpSession> getResponseSessions() {
        return new ArrayList<>(responseSessions.values());
    }

    @Override
    public void clearBuffers() {
        requestSessions.clear();
        responseSessions.clear();
        log.info("Packet buffers cleared");
    }

    @Override
    public boolean isReassemblyRunning() {
        return reassemblyRunning.get();
    }

    @Override
    public ProcessingStats getStats() {
        return new ProcessingStatsImpl();
    }

    private class ProcessingStatsImpl implements ProcessingStats {
        @Override
        public long getProcessedPackets() {
            return processedPackets.get();
        }

        @Override
        public long getReassembledSessions() {
            return reassembledSessions.get();
        }

        @Override
        public long getSentMessages() {
            return sentMessages.get();
        }

        @Override
        public long getErrorCount() {
            return errorCount.get();
        }

        @Override
        public int getCurrentBufferSize() {
            return requestSessions.size() + responseSessions.size();
        }

        @Override
        public long getDroppedPackets() {
            return droppedPackets.get();
        }
    }
}
