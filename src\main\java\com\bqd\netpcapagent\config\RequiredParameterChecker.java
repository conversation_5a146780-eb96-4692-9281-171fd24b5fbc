package com.bqd.netpcapagent.config;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-03-04
 */
@Component
public class RequiredParameterChecker implements ApplicationRunner {

    @Autowired
    private ApplicationArguments applicationArguments;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 检查是否有名为'a'的参数被传递
        if (CollectionUtil.isEmpty(applicationArguments.getOptionValues("hostIp"))) {
            throw new IllegalArgumentException("未找到启动参数hostIp，启动失败");
        }
    }
}
