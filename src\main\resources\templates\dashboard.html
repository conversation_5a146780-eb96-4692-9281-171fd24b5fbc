<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Packet Capture Agent - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .card-metric { text-align: center; }
        .metric-value { font-size: 2rem; font-weight: bold; }
        .metric-label { color: #6c757d; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-network-wired"></i> Packet Capture Agent</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a class="nav-link" href="/dashboard/config"><i class="fas fa-cog"></i> Configuration</a>
                <a class="nav-link" href="/dashboard/monitor"><i class="fas fa-chart-line"></i> Monitoring</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <hr>
            </div>
        </div>

        <!-- Status Cards -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <span th:class="${captureRunning} ? 'status-indicator status-running' : 'status-indicator status-stopped'"></span>
                            Packet Capture
                        </h5>
                        <p class="card-text" th:text="${captureRunning} ? 'Running' : 'Stopped'">Status</p>
                        <div th:if="${currentConfig != null}">
                            <small class="text-muted">
                                Device: <span th:text="${currentConfig.deviceName}">N/A</span><br>
                                Port: <span th:text="${currentConfig.port}">N/A</span><br>
                                Started: <span th:text="${#dates.format(new java.util.Date(currentConfig.startTime), 'yyyy-MM-dd HH:mm:ss')}">N/A</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <span th:class="${reassemblyRunning} ? 'status-indicator status-running' : 'status-indicator status-stopped'"></span>
                            Packet Reassembly
                        </h5>
                        <p class="card-text" th:text="${reassemblyRunning} ? 'Running' : 'Stopped'">Status</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metrics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-metric">
                    <div class="card-body">
                        <div class="metric-value text-primary" th:text="${stats.processedPackets}">0</div>
                        <div class="metric-label">Processed Packets</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-metric">
                    <div class="card-body">
                        <div class="metric-value text-success" th:text="${stats.reassembledSessions}">0</div>
                        <div class="metric-label">Reassembled Sessions</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-metric">
                    <div class="card-body">
                        <div class="metric-value text-info" th:text="${stats.sentMessages}">0</div>
                        <div class="metric-label">Sent Messages</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-metric">
                    <div class="card-body">
                        <div class="metric-value text-warning" th:text="${stats.currentBufferSize}">0</div>
                        <div class="metric-label">Buffer Size</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-play-circle"></i> Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <form id="captureForm" class="row g-3">
                            <div class="col-md-6">
                                <label for="deviceSelect" class="form-label">Network Interface</label>
                                <select class="form-select" id="deviceSelect" name="deviceName" required>
                                    <option value="">Select an interface...</option>
                                    <option th:each="interface : ${interfaces}" 
                                            th:value="${interface.name}" 
                                            th:text="${interface.name + ' - ' + interface.description}"
                                            th:disabled="${!interface.up}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="portInput" class="form-label">Port</label>
                                <input type="number" class="form-control" id="portInput" name="port" 
                                       min="1" max="65535" th:value="${properties.defaultPort}" required>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="btn btn-success me-2" id="startBtn" 
                                        th:disabled="${captureRunning}">
                                    <i class="fas fa-play"></i> Start
                                </button>
                                <button type="button" class="btn btn-danger" id="stopBtn" 
                                        th:disabled="${!captureRunning}">
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error/Success Messages -->
        <div id="alertContainer"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 5 seconds
        setInterval(() => {
            location.reload();
        }, 5000);

        // Control buttons
        document.getElementById('startBtn').addEventListener('click', function() {
            const deviceName = document.getElementById('deviceSelect').value;
            const port = document.getElementById('portInput').value;
            
            if (!deviceName || !port) {
                showAlert('Please select a device and enter a port number', 'danger');
                return;
            }
            
            fetch('/api/v1/capture/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `deviceName=${encodeURIComponent(deviceName)}&port=${encodeURIComponent(port)}`
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                showAlert('Error starting capture: ' + error.message, 'danger');
            });
        });

        document.getElementById('stopBtn').addEventListener('click', function() {
            fetch('/api/v1/capture/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            })
            .catch(error => {
                showAlert('Error stopping capture: ' + error.message, 'danger');
            });
        });

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
    </script>
</body>
</html>
