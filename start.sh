#!/bin/bash

# Network Packet Capture Agent Startup Script
# This script provides an easy way to start the packet capture agent with proper configuration

set -e

# Default configuration
DEFAULT_HOST_IP="127.0.0.1"
DEFAULT_PORT="80"
DEFAULT_ROCKETMQ_SERVER="localhost:9876"
DEFAULT_WORKER_THREADS="4"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help                    Show this help message"
    echo "  --host-ip IP                  Host IP address (default: $DEFAULT_HOST_IP)"
    echo "  --port PORT                   Default capture port (default: $DEFAULT_PORT)"
    echo "  --rocketmq-server SERVER      RocketMQ server address (default: $DEFAULT_ROCKETMQ_SERVER)"
    echo "  --worker-threads THREADS      Number of worker threads (default: $DEFAULT_WORKER_THREADS)"
    echo "  --docker                      Start using Docker Compose"
    echo "  --build                       Build the application before starting"
    echo "  --dev                         Start in development mode with debug logging"
    echo ""
    echo "Examples:"
    echo "  $0 --host-ip ************* --port 8080"
    echo "  $0 --docker"
    echo "  $0 --build --dev"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    local java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    print_info "Java version: $java_version"
    
    # Check Maven if building
    if [[ "$BUILD" == "true" ]]; then
        if ! command -v mvn &> /dev/null; then
            print_error "Maven is not installed or not in PATH"
            exit 1
        fi
        print_info "Maven found: $(mvn -version | head -n 1)"
    fi
    
    # Check Docker if using Docker mode
    if [[ "$DOCKER_MODE" == "true" ]]; then
        if ! command -v docker &> /dev/null; then
            print_error "Docker is not installed or not in PATH"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            print_error "Docker Compose is not installed or not in PATH"
            exit 1
        fi
        
        print_info "Docker found: $(docker --version)"
        print_info "Docker Compose found: $(docker-compose --version)"
    fi
    
    print_success "Prerequisites check completed"
}

# Function to build the application
build_application() {
    print_info "Building the application..."
    
    if [[ ! -f "pom.xml" ]]; then
        print_error "pom.xml not found. Please run this script from the project root directory."
        exit 1
    fi
    
    mvn clean package -DskipTests
    
    if [[ $? -eq 0 ]]; then
        print_success "Application built successfully"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to start with Docker
start_docker() {
    print_info "Starting with Docker Compose..."
    
    # Set environment variables
    export HOST_IP="$HOST_IP"
    export DEFAULT_PORT="$PORT"
    export ROCKETMQ_NAME_SERVER="$ROCKETMQ_SERVER"
    export WORKER_THREADS="$WORKER_THREADS"
    
    # Create logs directory
    mkdir -p logs
    
    # Start services
    docker-compose up -d
    
    if [[ $? -eq 0 ]]; then
        print_success "Services started successfully"
        print_info "Dashboard: http://localhost:25900/agent/net-pcap/dashboard"
        print_info "API: http://localhost:25900/agent/net-pcap/api/v1"
        print_info "RocketMQ Console: http://localhost:8080"
        print_info ""
        print_info "To view logs: docker-compose logs -f net-pcap-agent"
        print_info "To stop services: docker-compose down"
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Function to start standalone
start_standalone() {
    print_info "Starting standalone application..."
    
    # Find the JAR file
    JAR_FILE=$(find target -name "net-pcap-agent-*.jar" | head -n 1)
    
    if [[ -z "$JAR_FILE" ]]; then
        print_error "JAR file not found. Please build the application first with --build option."
        exit 1
    fi
    
    print_info "Using JAR file: $JAR_FILE"
    
    # Create logs directory
    mkdir -p logs
    
    # Set JVM options
    JVM_OPTS="-Xmx512m -Xms256m"
    
    if [[ "$DEV_MODE" == "true" ]]; then
        JVM_OPTS="$JVM_OPTS -Dlogging.level.com.bqd.netpcapagent=DEBUG"
        print_info "Starting in development mode with debug logging"
    fi
    
    # Set application properties
    APP_PROPS=""
    APP_PROPS="$APP_PROPS --packet-capture.host-ip=$HOST_IP"
    APP_PROPS="$APP_PROPS --packet-capture.default-port=$PORT"
    APP_PROPS="$APP_PROPS --rocketmq.name-server=$ROCKETMQ_SERVER"
    APP_PROPS="$APP_PROPS --packet-capture.performance.worker-threads=$WORKER_THREADS"
    
    print_info "Configuration:"
    print_info "  Host IP: $HOST_IP"
    print_info "  Default Port: $PORT"
    print_info "  RocketMQ Server: $ROCKETMQ_SERVER"
    print_info "  Worker Threads: $WORKER_THREADS"
    print_info ""
    
    # Start the application
    print_info "Starting application..."
    java $JVM_OPTS -jar "$JAR_FILE" $APP_PROPS
}

# Parse command line arguments
HOST_IP="$DEFAULT_HOST_IP"
PORT="$DEFAULT_PORT"
ROCKETMQ_SERVER="$DEFAULT_ROCKETMQ_SERVER"
WORKER_THREADS="$DEFAULT_WORKER_THREADS"
DOCKER_MODE="false"
BUILD="false"
DEV_MODE="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --host-ip)
            HOST_IP="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --rocketmq-server)
            ROCKETMQ_SERVER="$2"
            shift 2
            ;;
        --worker-threads)
            WORKER_THREADS="$2"
            shift 2
            ;;
        --docker)
            DOCKER_MODE="true"
            shift
            ;;
        --build)
            BUILD="true"
            shift
            ;;
        --dev)
            DEV_MODE="true"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
print_info "Network Packet Capture Agent Startup Script"
print_info "============================================"

# Check prerequisites
check_prerequisites

# Build if requested
if [[ "$BUILD" == "true" ]]; then
    build_application
fi

# Start the application
if [[ "$DOCKER_MODE" == "true" ]]; then
    start_docker
else
    start_standalone
fi
