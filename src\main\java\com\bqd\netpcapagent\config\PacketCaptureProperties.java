package com.bqd.netpcapagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Configuration properties for packet capture functionality
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@Component
@ConfigurationProperties(prefix = "packet-capture")
@Validated
public class PacketCaptureProperties {

    /**
     * Host IP for distinguishing request/response packets
     */
    @NotBlank(message = "Host IP cannot be blank")
    private String hostIp = "127.0.0.1";

    /**
     * Default network interface
     */
    private String defaultInterface;

    /**
     * Default port to capture
     */
    @Min(value = 1, message = "Port must be greater than 0")
    private int defaultPort = 80;

    /**
     * Packet buffer configuration
     */
    @Valid
    @NotNull
    private Buffer buffer = new Buffer();

    /**
     * HTTP reassembly configuration
     */
    @Valid
    @NotNull
    private Reassembly reassembly = new Reassembly();

    /**
     * Performance tuning configuration
     */
    @Valid
    @NotNull
    private Performance performance = new Performance();

    @Data
    public static class Buffer {
        /**
         * Maximum number of packets to buffer before processing
         */
        @Min(value = 100, message = "Buffer max size must be at least 100")
        private int maxSize = 10000;

        /**
         * Buffer cleanup interval in seconds
         */
        @Min(value = 10, message = "Cleanup interval must be at least 10 seconds")
        private int cleanupInterval = 60;

        /**
         * Maximum age of packets in buffer before cleanup (seconds)
         */
        @Min(value = 30, message = "Max age must be at least 30 seconds")
        private int maxAge = 300;
    }

    @Data
    public static class Reassembly {
        /**
         * Default confirmation duration in seconds
         */
        @Min(value = 1, message = "Confirm duration must be at least 1 second")
        private long defaultConfirmDuration = 5;

        /**
         * Maximum time to wait for packet reassembly (seconds)
         */
        @Min(value = 5, message = "Max wait time must be at least 5 seconds")
        private int maxWaitTime = 30;

        /**
         * Batch size for processing packets
         */
        @Min(value = 1, message = "Batch size must be at least 1")
        private int batchSize = 100;
    }

    @Data
    public static class Performance {
        /**
         * Number of worker threads for packet processing
         */
        @Min(value = 1, message = "Worker threads must be at least 1")
        private int workerThreads = 4;

        /**
         * Pcap buffer size
         */
        @Min(value = 1024, message = "Pcap buffer size must be at least 1024")
        private int pcapBufferSize = 65536;

        /**
         * Pcap timeout in milliseconds
         */
        @Min(value = 100, message = "Pcap timeout must be at least 100ms")
        private int pcapTimeout = 1000;
    }
}
