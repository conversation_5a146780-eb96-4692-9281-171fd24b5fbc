package com.bqd.netpcapagent.service;

import java.util.List;

/**
 * Service interface for packet capture operations
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
public interface PacketCaptureService {
    
    /**
     * Get list of available network interfaces
     * 
     * @return list of network interface names
     */
    List<NetworkInterface> getNetworkInterfaces();
    
    /**
     * Start packet capture on specified interface and port
     * 
     * @param deviceName network interface name
     * @param port port number to capture
     */
    void startCapture(String deviceName, int port);
    
    /**
     * Stop packet capture
     */
    void stopCapture();
    
    /**
     * Check if packet capture is currently running
     * 
     * @return true if capture is running
     */
    boolean isCaptureRunning();
    
    /**
     * Get current capture configuration
     * 
     * @return capture configuration or null if not running
     */
    CaptureConfig getCurrentConfig();
    
    /**
     * Network interface information
     */
    interface NetworkInterface {
        String getName();
        String getDescription();
        List<String> getAddresses();
        boolean isUp();
        boolean isLoopback();
    }
    
    /**
     * Capture configuration
     */
    interface CaptureConfig {
        String getDeviceName();
        int getPort();
        long getStartTime();
        long getCapturedPackets();
    }
}
