package com.bqd.netpcapagent.metrics;

import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Metrics component for packet capture functionality
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Component
public class PacketCaptureMetrics {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @PostConstruct
    public void initMetrics() {
        // Capture status metrics
        Gauge.builder("packet_capture.running")
                .description("Whether packet capture is currently running")
                .register(meterRegistry, this, metrics -> packetCaptureService.isCaptureRunning() ? 1.0 : 0.0);

        Gauge.builder("packet_reassembly.running")
                .description("Whether packet reassembly is currently running")
                .register(meterRegistry, this, metrics -> packetProcessingService.isReassemblyRunning() ? 1.0 : 0.0);

        // Processing statistics
        Gauge.builder("packet_processing.processed_packets")
                .description("Total number of processed packets")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getProcessedPackets());

        Gauge.builder("packet_processing.reassembled_sessions")
                .description("Total number of reassembled sessions")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getReassembledSessions());

        Gauge.builder("packet_processing.sent_messages")
                .description("Total number of messages sent to RocketMQ")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getSentMessages());

        Gauge.builder("packet_processing.error_count")
                .description("Total number of processing errors")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getErrorCount());

        Gauge.builder("packet_processing.current_buffer_size")
                .description("Current number of packets in buffer")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getCurrentBufferSize());

        Gauge.builder("packet_processing.dropped_packets")
                .description("Total number of dropped packets")
                .register(meterRegistry, this, metrics -> (double) packetProcessingService.getStats().getDroppedPackets());

        // Capture configuration metrics
        Gauge.builder("packet_capture.current_port")
                .description("Currently configured capture port")
                .register(meterRegistry, this, metrics -> {
                    PacketCaptureService.CaptureConfig config = packetCaptureService.getCurrentConfig();
                    return config != null ? (double) config.getPort() : 0.0;
                });

        Gauge.builder("packet_capture.captured_packets")
                .description("Number of packets captured in current session")
                .register(meterRegistry, this, metrics -> {
                    PacketCaptureService.CaptureConfig config = packetCaptureService.getCurrentConfig();
                    return config != null ? (double) config.getCapturedPackets() : 0.0;
                });

        // Derived metrics
        Gauge.builder("packet_processing.error_rate")
                .description("Error rate as percentage of processed packets")
                .register(meterRegistry, this, metrics -> {
                    PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
                    long processed = stats.getProcessedPackets();
                    long errors = stats.getErrorCount();
                    return processed > 0 ? (double) errors / processed * 100.0 : 0.0;
                });

        Gauge.builder("packet_processing.drop_rate")
                .description("Drop rate as percentage of total packets")
                .register(meterRegistry, this, metrics -> {
                    PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
                    long processed = stats.getProcessedPackets();
                    long dropped = stats.getDroppedPackets();
                    long total = processed + dropped;
                    return total > 0 ? (double) dropped / total * 100.0 : 0.0;
                });

        Gauge.builder("packet_processing.reassembly_rate")
                .description("Reassembly rate as percentage of processed packets")
                .register(meterRegistry, this, metrics -> {
                    PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
                    long processed = stats.getProcessedPackets();
                    long reassembled = stats.getReassembledSessions() * 2; // Each session represents 2 packets (req+resp)
                    return processed > 0 ? (double) reassembled / processed * 100.0 : 0.0;
                });
    }
}
