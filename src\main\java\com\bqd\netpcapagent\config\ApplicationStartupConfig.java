package com.bqd.netpcapagent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Application startup configuration and validation
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Slf4j
@Component
public class ApplicationStartupConfig {

    @Autowired
    private PacketCaptureProperties properties;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== Network Packet Capture Agent Started ===");
        log.info("Host IP: {}", properties.getHostIp());
        log.info("Default Interface: {}", properties.getDefaultInterface());
        log.info("Default Port: {}", properties.getDefaultPort());
        log.info("Worker Threads: {}", properties.getPerformance().getWorkerThreads());
        log.info("Buffer Max Size: {}", properties.getBuffer().getMaxSize());
        log.info("Dashboard available at: http://localhost:25900/agent/net-pcap/dashboard");
        log.info("API available at: http://localhost:25900/agent/net-pcap/api/v1");
        log.info("===========================================");
    }
}
