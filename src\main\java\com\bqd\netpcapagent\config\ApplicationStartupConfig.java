package com.bqd.netpcapagent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Application startup configuration and validation
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Slf4j
@Component
public class ApplicationStartupConfig {

    @Autowired
    private PacketCaptureProperties properties;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("=== Network Packet Capture Agent Started ===");
        log.info("Deployment Mode: JAR-based standalone");
        log.info("Host IP: {}", properties.getHostIp());
        log.info("Default Interface: {}", properties.getDefaultInterface());
        log.info("Default Port: {}", properties.getDefaultPort());
        log.info("Worker Threads: {}", properties.getPerformance().getWorkerThreads());
        log.info("Buffer Max Size: {}", properties.getBuffer().getMaxSize());
        log.info("Pcap Buffer Size: {}", properties.getPerformance().getPcapBufferSize());
        log.info("");
        log.info("Access Points:");
        log.info("  Dashboard: http://localhost:25900/agent/net-pcap/dashboard");
        log.info("  REST API: http://localhost:25900/agent/net-pcap/api/v1");
        log.info("  Health Check: http://localhost:25900/agent/net-pcap/actuator/health");
        log.info("  Metrics: http://localhost:25900/agent/net-pcap/actuator/metrics");
        log.info("");
        log.info("Use the web dashboard to start packet capture or use the REST API");
        log.info("===========================================");
    }
}
