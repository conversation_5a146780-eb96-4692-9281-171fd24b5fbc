package com.bqd.netpcapagent.controller;

import com.bqd.netpcapagent.handler.PacketHandler;
import com.bqd.netpcapagent.service.AgentService;
import com.bqd.netpcapagent.service.impl.AgentServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @CreateTime 2025-02-28
 */
@RequestMapping("/")
@RestController
public class AgentController {

    @Autowired
    private AgentService agentService;

    /**
     * 获取网卡列表
     * @return
     */
    @GetMapping("/deviceList")
    public ResponseEntity<List<String>> deviceList() {
        return ResponseEntity.ok(agentService.deviceList());
    }

    /**
     * 开始抓包
     * @param deviceName
     * @param port
     */
    @GetMapping("/capture")
    public void capture(@RequestParam String deviceName, @RequestParam String port) {
        agentService.capture(deviceName, port);
    }

    /**
     * 将tcp包重组成http包并发送到mq
     */
    @GetMapping("/reassemblePacketAndSend")
    public void reassemblePacketAndSend(@RequestParam Long confirmDuration) {
        agentService.reassemblePacketAndSend(confirmDuration);
    }

    /**
     * 获取录制的请求tcp包
     * @return
     */
    @GetMapping("/reqtPacketMap")
    public ResponseEntity<Map<Long, PacketHandler.TcpSession>> reqtPacketMap() {
        return ResponseEntity.ok(PacketHandler.reqtPacketMap);
    }

    /**
     * 获取录制的响应tcp包
     * @return
     */
    @GetMapping("/respPacketMap")
    public ResponseEntity<Map<Long, PacketHandler.TcpSession>> respPacketMap() {
        return ResponseEntity.ok(PacketHandler.respPacketMap);
    }

    /**
     * 获取抓包状态
     * @return
     */
    @GetMapping("/status")
    public ResponseEntity<Boolean> status() {
        return ResponseEntity.ok(AgentServiceImpl.status);
    }

    /**
     * 停止抓包
     */
    @GetMapping("/stop")
    public void stop() {
        AgentServiceImpl.status = false;
    }
}
