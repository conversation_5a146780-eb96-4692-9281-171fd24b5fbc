package com.bqd.netpcapagent.controller;

import com.bqd.netpcapagent.model.TcpSession;
import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST API controller for packet capture operations
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Slf4j
@RestController
@RequestMapping("/api/v1")
@Validated
public class AgentController {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    /**
     * Get list of available network interfaces
     *
     * @return list of network interfaces
     */
    @GetMapping("/interfaces")
    public ResponseEntity<List<PacketCaptureService.NetworkInterface>> getNetworkInterfaces() {
        List<PacketCaptureService.NetworkInterface> interfaces = packetCaptureService.getNetworkInterfaces();
        return ResponseEntity.ok(interfaces);
    }

    /**
     * Start packet capture
     *
     * @param deviceName network interface name
     * @param port port number to capture
     * @return success response
     */
    @PostMapping("/capture/start")
    public ResponseEntity<Map<String, Object>> startCapture(
            @RequestParam @NotBlank(message = "Device name cannot be blank") String deviceName,
            @RequestParam @Min(value = 1, message = "Port must be greater than 0")
                         @Max(value = 65535, message = "Port must be less than 65536") int port) {

        packetCaptureService.startCapture(deviceName, port);
        packetProcessingService.startReassemblyProcess();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Packet capture started successfully");
        response.put("deviceName", deviceName);
        response.put("port", port);
        response.put("timestamp", System.currentTimeMillis());

        log.info("Packet capture started on device {} port {}", deviceName, port);
        return ResponseEntity.ok(response);
    }

    /**
     * Stop packet capture
     *
     * @return success response
     */
    @PostMapping("/capture/stop")
    public ResponseEntity<Map<String, Object>> stopCapture() {
        packetCaptureService.stopCapture();
        packetProcessingService.stopReassemblyProcess();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Packet capture stopped successfully");
        response.put("timestamp", System.currentTimeMillis());

        log.info("Packet capture stopped");
        return ResponseEntity.ok(response);
    }

    /**
     * Get capture status and configuration
     *
     * @return capture status information
     */
    @GetMapping("/capture/status")
    public ResponseEntity<Map<String, Object>> getCaptureStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("captureRunning", packetCaptureService.isCaptureRunning());
        status.put("reassemblyRunning", packetProcessingService.isReassemblyRunning());
        status.put("currentConfig", packetCaptureService.getCurrentConfig());
        status.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(status);
    }

    /**
     * Get processing statistics
     *
     * @return processing statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<PacketProcessingService.ProcessingStats> getProcessingStats() {
        PacketProcessingService.ProcessingStats stats = packetProcessingService.getStats();
        return ResponseEntity.ok(stats);
    }

    /**
     * Get current request sessions
     *
     * @return list of request sessions
     */
    @GetMapping("/sessions/requests")
    public ResponseEntity<List<TcpSession>> getRequestSessions() {
        List<TcpSession> sessions = packetProcessingService.getRequestSessions();
        return ResponseEntity.ok(sessions);
    }

    /**
     * Get current response sessions
     *
     * @return list of response sessions
     */
    @GetMapping("/sessions/responses")
    public ResponseEntity<List<TcpSession>> getResponseSessions() {
        List<TcpSession> sessions = packetProcessingService.getResponseSessions();
        return ResponseEntity.ok(sessions);
    }

    /**
     * Clear all packet buffers
     *
     * @return success response
     */
    @PostMapping("/sessions/clear")
    public ResponseEntity<Map<String, Object>> clearSessions() {
        packetProcessingService.clearBuffers();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Packet buffers cleared successfully");
        response.put("timestamp", System.currentTimeMillis());

        log.info("Packet buffers cleared");
        return ResponseEntity.ok(response);
    }
}
