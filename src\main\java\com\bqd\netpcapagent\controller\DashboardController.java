package com.bqd.netpcapagent.controller;

import com.bqd.netpcapagent.config.PacketCaptureProperties;
import com.bqd.netpcapagent.service.PacketCaptureService;
import com.bqd.netpcapagent.service.PacketProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * Web dashboard controller for the packet capture agent
 * 
 * <AUTHOR>
 * @since 2025-02-28
 */
@Controller
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private PacketCaptureService packetCaptureService;

    @Autowired
    private PacketProcessingService packetProcessingService;

    @Autowired
    private PacketCaptureProperties properties;

    /**
     * Main dashboard page
     */
    @GetMapping({"", "/"})
    public String dashboard(Model model) {
        model.addAttribute("interfaces", packetCaptureService.getNetworkInterfaces());
        model.addAttribute("captureRunning", packetCaptureService.isCaptureRunning());
        model.addAttribute("reassemblyRunning", packetProcessingService.isReassemblyRunning());
        model.addAttribute("currentConfig", packetCaptureService.getCurrentConfig());
        model.addAttribute("stats", packetProcessingService.getStats());
        model.addAttribute("properties", properties);
        
        return "dashboard";
    }

    /**
     * Configuration page
     */
    @GetMapping("/config")
    public String configuration(Model model) {
        model.addAttribute("properties", properties);
        model.addAttribute("interfaces", packetCaptureService.getNetworkInterfaces());
        
        return "config";
    }

    /**
     * Monitoring page
     */
    @GetMapping("/monitor")
    public String monitoring(Model model) {
        model.addAttribute("stats", packetProcessingService.getStats());
        model.addAttribute("requestSessions", packetProcessingService.getRequestSessions());
        model.addAttribute("responseSessions", packetProcessingService.getResponseSessions());
        
        return "monitor";
    }
}
